import { useState, useEffect } from 'react';
import { getVendorById } from '../services/prismaVendorService';

const VendorDebug = ({ vendorId }) => {
  const [vendor, setVendor] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchVendor = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('🔄 Debug: Fetching vendor with ID:', vendorId);
        
        const vendorData = await getVendorById(vendorId);
        console.log('📦 Debug: Raw vendor data:', vendorData);
        
        setVendor(vendorData);
      } catch (err) {
        console.error('❌ Debug: Error fetching vendor:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (vendorId) {
      fetchVendor();
    }
  }, [vendorId]);

  if (loading) {
    return <div className="p-4 bg-blue-50 border border-blue-200 rounded">Loading vendor {vendorId}...</div>;
  }

  if (error) {
    return <div className="p-4 bg-red-50 border border-red-200 rounded">Error: {error}</div>;
  }

  if (!vendor) {
    return <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">No vendor found with ID: {vendorId}</div>;
  }

  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded">
      <h3 className="font-bold text-green-800 mb-2">Vendor Debug Info</h3>
      <div className="space-y-2 text-sm">
        <p><strong>ID:</strong> {vendor.id}</p>
        <p><strong>Company Name:</strong> {vendor.companyName}</p>
        <p><strong>Email:</strong> {vendor.emails?.[0] || 'N/A'}</p>
        <p><strong>Phone:</strong> {vendor.phones?.[0] || 'N/A'}</p>
        <p><strong>Status:</strong> {vendor.status}</p>
        <p><strong>Rating:</strong> {vendor.rating}</p>
        <details className="mt-4">
          <summary className="cursor-pointer font-medium">Full Data</summary>
          <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto max-h-64">
            {JSON.stringify(vendor, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default VendorDebug;
