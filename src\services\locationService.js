/**
 * Location Service
 * Handles states and cities data from Indian Government API
 */

const STATES_API_URL = 'https://igod.gov.in/sg/district/states';
const DISTRICTS_API_URL = 'https://igod.gov.in/sg/district/districts';

// Cache for states and districts to avoid repeated API calls
let statesCache = null;
let districtsCache = {};

/**
 * Fetch all states from government API
 */
export const getAllStates = async () => {
  try {
    // Return cached data if available
    if (statesCache) {
      console.log('📍 Returning cached states data');
      return statesCache;
    }

    console.log('🔄 Fetching states from government API...');
    
    const response = await fetch(STATES_API_URL, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    // Transform the data to a more usable format
    const states = data.map(state => ({
      id: state.state_id || state.id,
      name: state.state_name || state.name,
      code: state.state_code || state.code
    }));

    // Sort states alphabetically
    states.sort((a, b) => a.name.localeCompare(b.name));

    // Cache the results
    statesCache = states;
    
    console.log(`✅ Fetched ${states.length} states successfully`);
    return states;

  } catch (error) {
    console.error('❌ Error fetching states:', error);
    
    // Fallback to hardcoded major Indian states if API fails
    const fallbackStates = [
      { id: '1', name: 'Andhra Pradesh', code: 'AP' },
      { id: '2', name: 'Arunachal Pradesh', code: 'AR' },
      { id: '3', name: 'Assam', code: 'AS' },
      { id: '4', name: 'Bihar', code: 'BR' },
      { id: '5', name: 'Chhattisgarh', code: 'CG' },
      { id: '6', name: 'Goa', code: 'GA' },
      { id: '7', name: 'Gujarat', code: 'GJ' },
      { id: '8', name: 'Haryana', code: 'HR' },
      { id: '9', name: 'Himachal Pradesh', code: 'HP' },
      { id: '10', name: 'Jharkhand', code: 'JH' },
      { id: '11', name: 'Karnataka', code: 'KA' },
      { id: '12', name: 'Kerala', code: 'KL' },
      { id: '13', name: 'Madhya Pradesh', code: 'MP' },
      { id: '14', name: 'Maharashtra', code: 'MH' },
      { id: '15', name: 'Manipur', code: 'MN' },
      { id: '16', name: 'Meghalaya', code: 'ML' },
      { id: '17', name: 'Mizoram', code: 'MZ' },
      { id: '18', name: 'Nagaland', code: 'NL' },
      { id: '19', name: 'Odisha', code: 'OR' },
      { id: '20', name: 'Punjab', code: 'PB' },
      { id: '21', name: 'Rajasthan', code: 'RJ' },
      { id: '22', name: 'Sikkim', code: 'SK' },
      { id: '23', name: 'Tamil Nadu', code: 'TN' },
      { id: '24', name: 'Telangana', code: 'TG' },
      { id: '25', name: 'Tripura', code: 'TR' },
      { id: '26', name: 'Uttar Pradesh', code: 'UP' },
      { id: '27', name: 'Uttarakhand', code: 'UK' },
      { id: '28', name: 'West Bengal', code: 'WB' },
      { id: '29', name: 'Delhi', code: 'DL' },
      { id: '30', name: 'Jammu and Kashmir', code: 'JK' },
      { id: '31', name: 'Ladakh', code: 'LA' },
      { id: '32', name: 'Puducherry', code: 'PY' },
      { id: '33', name: 'Chandigarh', code: 'CH' },
      { id: '34', name: 'Dadra and Nagar Haveli and Daman and Diu', code: 'DN' },
      { id: '35', name: 'Lakshadweep', code: 'LD' },
      { id: '36', name: 'Andaman and Nicobar Islands', code: 'AN' }
    ];

    console.log('⚠️ Using fallback states data');
    statesCache = fallbackStates;
    return fallbackStates;
  }
};

/**
 * Fetch districts/cities for a specific state
 */
export const getDistrictsByState = async (stateId) => {
  try {
    // Return cached data if available
    if (districtsCache[stateId]) {
      console.log(`📍 Returning cached districts for state ${stateId}`);
      return districtsCache[stateId];
    }

    console.log(`🔄 Fetching districts for state ${stateId}...`);
    
    const response = await fetch(`${DISTRICTS_API_URL}?state_id=${stateId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    // Transform the data to a more usable format
    const districts = data.map(district => ({
      id: district.district_id || district.id,
      name: district.district_name || district.name,
      stateId: stateId
    }));

    // Sort districts alphabetically
    districts.sort((a, b) => a.name.localeCompare(b.name));

    // Cache the results
    districtsCache[stateId] = districts;
    
    console.log(`✅ Fetched ${districts.length} districts for state ${stateId}`);
    return districts;

  } catch (error) {
    console.error(`❌ Error fetching districts for state ${stateId}:`, error);
    
    // Fallback to some major cities based on state
    const fallbackDistricts = getFallbackDistricts(stateId);
    
    console.log(`⚠️ Using fallback districts for state ${stateId}`);
    districtsCache[stateId] = fallbackDistricts;
    return fallbackDistricts;
  }
};

/**
 * Get fallback districts for major states
 */
const getFallbackDistricts = (stateId) => {
  const fallbackData = {
    '11': [ // Karnataka
      { id: '1', name: 'Bangalore Urban', stateId: '11' },
      { id: '2', name: 'Bangalore Rural', stateId: '11' },
      { id: '3', name: 'Mysore', stateId: '11' },
      { id: '4', name: 'Mangalore', stateId: '11' },
      { id: '5', name: 'Hubli', stateId: '11' },
      { id: '6', name: 'Belgaum', stateId: '11' },
      { id: '7', name: 'Gulbarga', stateId: '11' },
      { id: '8', name: 'Davangere', stateId: '11' },
      { id: '9', name: 'Bellary', stateId: '11' },
      { id: '10', name: 'Bijapur', stateId: '11' }
    ],
    '14': [ // Maharashtra
      { id: '1', name: 'Mumbai', stateId: '14' },
      { id: '2', name: 'Pune', stateId: '14' },
      { id: '3', name: 'Nagpur', stateId: '14' },
      { id: '4', name: 'Nashik', stateId: '14' },
      { id: '5', name: 'Aurangabad', stateId: '14' },
      { id: '6', name: 'Solapur', stateId: '14' },
      { id: '7', name: 'Kolhapur', stateId: '14' },
      { id: '8', name: 'Sangli', stateId: '14' },
      { id: '9', name: 'Ahmednagar', stateId: '14' },
      { id: '10', name: 'Thane', stateId: '14' }
    ],
    '29': [ // Delhi
      { id: '1', name: 'Central Delhi', stateId: '29' },
      { id: '2', name: 'East Delhi', stateId: '29' },
      { id: '3', name: 'New Delhi', stateId: '29' },
      { id: '4', name: 'North Delhi', stateId: '29' },
      { id: '5', name: 'North East Delhi', stateId: '29' },
      { id: '6', name: 'North West Delhi', stateId: '29' },
      { id: '7', name: 'Shahdara', stateId: '29' },
      { id: '8', name: 'South Delhi', stateId: '29' },
      { id: '9', name: 'South East Delhi', stateId: '29' },
      { id: '10', name: 'South West Delhi', stateId: '29' },
      { id: '11', name: 'West Delhi', stateId: '29' }
    ],
    '23': [ // Tamil Nadu
      { id: '1', name: 'Chennai', stateId: '23' },
      { id: '2', name: 'Coimbatore', stateId: '23' },
      { id: '3', name: 'Madurai', stateId: '23' },
      { id: '4', name: 'Tiruchirappalli', stateId: '23' },
      { id: '5', name: 'Salem', stateId: '23' },
      { id: '6', name: 'Tirunelveli', stateId: '23' },
      { id: '7', name: 'Erode', stateId: '23' },
      { id: '8', name: 'Vellore', stateId: '23' },
      { id: '9', name: 'Thoothukudi', stateId: '23' },
      { id: '10', name: 'Dindigul', stateId: '23' }
    ]
  };

  return fallbackData[stateId] || [
    { id: '1', name: 'District 1', stateId: stateId },
    { id: '2', name: 'District 2', stateId: stateId },
    { id: '3', name: 'District 3', stateId: stateId }
  ];
};

/**
 * Search states by name
 */
export const searchStates = async (searchTerm) => {
  try {
    const states = await getAllStates();
    return states.filter(state => 
      state.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching states:', error);
    return [];
  }
};

/**
 * Search districts by name within a state
 */
export const searchDistricts = async (stateId, searchTerm) => {
  try {
    const districts = await getDistrictsByState(stateId);
    return districts.filter(district => 
      district.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching districts:', error);
    return [];
  }
};

/**
 * Clear cache (useful for refreshing data)
 */
export const clearLocationCache = () => {
  statesCache = null;
  districtsCache = {};
  console.log('🗑️ Location cache cleared');
};
