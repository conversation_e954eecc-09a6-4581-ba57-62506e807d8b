import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.VITE_DATABASE_URL;
const sql = neon(DATABASE_URL);

console.log('🔍 Checking vendors in database...');

try {
  // Check vendors count
  const vendorCount = await sql`SELECT COUNT(*) as count FROM vendors`;
  console.log(`📊 Total vendors: ${vendorCount[0].count}`);

  // Get all vendors
  const vendors = await sql`
    SELECT 
      id, 
      company_name, 
      company_type, 
      onboarding_date, 
      emails, 
      phones, 
      status,
      created_at
    FROM vendors 
    ORDER BY created_at DESC 
    LIMIT 5
  `;

  console.log('📋 Recent vendors:');
  vendors.forEach((vendor, index) => {
    console.log(`${index + 1}. ID: ${vendor.id}, Company: ${vendor.company_name}, Status: ${vendor.status}`);
  });

  if (vendors.length === 0) {
    console.log('⚠️ No vendors found in database. You may need to seed some data.');
  }

} catch (error) {
  console.error('❌ Error checking vendors:', error);
}
