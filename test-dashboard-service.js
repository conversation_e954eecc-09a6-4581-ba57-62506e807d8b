import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

dotenv.config();
const sql = neon(process.env.DATABASE_URL);

console.log('🧪 Testing dashboard queries...\n');

try {
  console.log('1. Testing client count:');
  const clients = await sql`SELECT COUNT(*) as count FROM clients`;
  console.log('📊 Clients count:', clients[0].count);

  console.log('\n2. Testing vendor count:');
  const vendors = await sql`SELECT COUNT(*) as count FROM vendors WHERE status = 'Active'`;
  console.log('👥 Active vendors count:', vendors[0].count);

  console.log('\n3. Testing type of work count:');
  const typeOfWork = await sql`SELECT COUNT(*) as count FROM type_of_work WHERE "isActive" = true`;
  console.log('🔧 Active type of work count:', typeOfWork[0].count);

  console.log('\n✅ All dashboard queries working!');
} catch (error) {
  console.error('❌ Test failed:', error.message);
}
