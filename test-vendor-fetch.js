import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;
const sql = neon(DATABASE_URL);

console.log('🔄 Testing vendor fetch...');

try {
  // Test fetching vendor with ID 70 (Wipster Technologies)
  const vendors = await sql`
    SELECT * FROM vendors WHERE id = 70
  `;

  if (vendors.length > 0) {
    console.log('✅ Vendor 70 fetched successfully:');
    console.log('📋 Vendor data:', JSON.stringify(vendors[0], null, 2));
  } else {
    console.log('❌ Vendor 70 not found');
  }
} catch (error) {
  console.error('❌ Error fetching vendor 70:', error);
}

try {
  // Test fetching vendor with ID 68 (TechCorp Solutions)
  const vendors2 = await sql`
    SELECT * FROM vendors WHERE id = 68
  `;

  if (vendors2.length > 0) {
    console.log('✅ Vendor 68 fetched successfully:');
    console.log('📋 Vendor data:', JSON.stringify(vendors2[0], null, 2));
  } else {
    console.log('❌ Vendor 68 not found');
  }
} catch (error) {
  console.error('❌ Error fetching vendor 68:', error);
}
